"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.where)(\"email\", \"==\", user.email));\n            const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado para um chat específico\n    const saveLastUsedModelForChat = async (modelId, chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            // Atualizar o lastUsedModel no documento do chat\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(chatRef, {\n                lastUsedModel: modelId,\n                lastModelUpdateAt: Date.now()\n            });\n            console.log(\"Last used model saved for chat:\", {\n                chatId,\n                modelId\n            });\n        } catch (error) {\n            console.error(\"Error saving last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado de um chat específico\n    const loadLastUsedModelForChat = async (chatId)=>{\n        if (!user || !chatId) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(chatRef);\n            if (chatDoc.exists()) {\n                const data = chatDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model for chat:\", {\n                        chatId,\n                        model: data.lastUsedModel\n                    });\n                } else {\n                    // Se o chat não tem modelo salvo, usar o modelo padrão\n                    setSelectedModel(\"meta-llama/llama-3.1-8b-instruct:free\");\n                    console.log(\"No saved model for chat, using default:\", chatId);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model for chat:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado globalmente (fallback)\n    const loadGlobalLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded global last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading global last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        // Salvar no chat específico se houver um chat ativo\n        if (actualChatId) {\n            saveLastUsedModelForChat(modelId, actualChatId);\n        }\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Salvar o modelo usado no chat específico\n            if (chatIdToUse) {\n                saveLastUsedModelForChat(selectedModel, chatIdToUse);\n            }\n            // Atualizar saldo do OpenRouter após a resposta\n            if (onUpdateOpenRouterBalance) {\n                onUpdateOpenRouterBalance();\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                setChatName(chatName);\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado globalmente quando o componente montar (apenas se não há chat)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && !currentChat) {\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        user,\n        currentChat\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n            // Carregar o modelo específico do chat\n            loadLastUsedModelForChat(currentChat);\n        } else if (!currentChat && actualChatId) {\n            // Só resetar se realmente não há chat e havia um chat antes\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n            // Carregar modelo global quando não há chat específico\n            loadGlobalLastUsedModel();\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex + 1);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            for(let i = messageIndex + 1; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        const scrollContainer = (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.querySelector(\".overflow-y-auto\");\n        if (scrollContainer) {\n            scrollContainer.scrollTo({\n                top: scrollContainer.scrollHeight,\n                behavior: \"smooth\"\n            });\n        }\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>{\n                    var _msg_attachments;\n                    return ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) > 0;\n                }).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 845,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 860,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 859,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 872,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 890,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 898,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 906,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 843,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"ldlbb13fosECRtXfe3XXDtGCF9o=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});